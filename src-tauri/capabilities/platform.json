{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "platform-capability", "description": "Capability for platform windows with remote URLs", "windows": ["embedded-*"], "remote": {"urls": ["https://chatgpt.com", "https://claude.ai/chat", "https://gemini.google.com", "https://copilot.microsoft.com", "https://www.perplexity.ai", "https://chat.deepseek.com", "https://kimi.com", "https://chat.qwen.ai", "https://www.meta.ai", "https://chat.mistral.ai", "https://grok.com", "https://poe.com", "https://www.midjourney.com", "https://openai.com/index/dall-e-3/", "https://leonardo.ai", "https://runwayml.com", "https://ideogram.ai", "https://suno.com", "https://www.udio.com", "https://pika.art", "https://elevenlabs.io", "https://lumalabs.ai", "https://www.adobe.com/in/products/firefly.html", "https://clipdrop.co", "https://notebooklm.google.com", "https://www.notion.so/product/ai", "https://github.com/features/copilot", "https://replit.com/ai", "https://cursor.sh", "https://www.phind.com", "https://www.langchain.com", "https://www.grammarly.com/ai", "https://consensus.app", "https://elicit.com", "https://you.com", "https://typeset.io", "https://huggingface.co", "https://lmstudio.ai", "https://openrouter.ai", "https://replicate.com", "https://console.groq.com", "https://fireworks.ai", "https://ollama.com", "https://lmarena.ai/", "https://vercel.com/ai", "https://www.together.ai", "https://www.futurepedia.io", "https://theresanaiforthat.com"]}, "permissions": ["core:path:default", "core:event:default", "core:window:default", "core:app:default", "core:resources:default"]}