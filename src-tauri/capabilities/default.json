{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "main-capability", "description": "Capability for the main window", "windows": ["main"], "permissions": ["core:path:default", "core:event:default", "core:window:default", "core:webview:default", "core:app:default", "core:resources:default", "core:menu:default", "core:tray:default", "core:window:allow-create", "core:window:allow-set-focus", "core:window:allow-show", "core:webview:allow-create-webview-window", "core:webview:allow-webview-close", "core:webview:allow-webview-position", "core:webview:allow-webview-size", "shell:allow-open"]}