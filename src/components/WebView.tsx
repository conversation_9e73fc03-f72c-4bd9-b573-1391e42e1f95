import { useEffect, useState, useRef } from 'react';
import { useAppStore } from '../store';
import { WebviewWindow } from '@tauri-apps/api/webviewWindow';
import { open } from '@tauri-apps/plugin-shell';

export const WebView = () => {
  const { activePlatform } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);
  const [webviewError, setWebviewError] = useState<string | null>(null);
  const windowRef = useRef<WebviewWindow | null>(null);

  useEffect(() => {
    if (windowRef.current) {
      windowRef.current.close().catch(console.error);
      windowRef.current = null;
    }

    if (!activePlatform) return;

    setIsLoading(true);
    setWebviewError(null);

    const label = `platform-${activePlatform.id}`;

    const createPlatformWindow = async () => {
      try {
        let platformWindow = await WebviewWindow.getByLabel(label);

        if (!platformWindow) {
          platformWindow = new WebviewWindow(label, {
            url: activePlatform.url,
            title: activePlatform.name,
            width: 1200,
            height: 800,
            resizable: true,
            center: true,
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          });

          // Listen for window creation events
          platformWindow.once('tauri://created', () => {
            setIsLoading(false);
          });

          platformWindow.once('tauri://error', (event) => {
            console.error(`WebView error for ${activePlatform.name}:`, event);
            setWebviewError(`${activePlatform.name} failed to load.`);
            setIsLoading(false);
          });
        } else {
          await platformWindow.show();
          await platformWindow.setFocus();
          setIsLoading(false);
        }

        windowRef.current = platformWindow;
      } catch (error) {
        console.error(`Failed to create window for ${activePlatform.name}:`, error);
        setWebviewError(`Could not open ${activePlatform.name}.`);
        setIsLoading(false);
      }
    };

    createPlatformWindow();

    return () => {
      if (windowRef.current) {
        windowRef.current.close().catch(console.error);
        windowRef.current = null;
      }
    };
  }, [activePlatform]);

  const openInExternalBrowser = async () => {
    if (activePlatform) {
      try {
        await open(activePlatform.url);
      } catch (error) {
        console.error('Failed to open external browser:', error);
      }
    }
  };

  const EmptyState = () => (
    <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-bgPrimary to-bgTertiary">
      <div className="text-center space-y-6 max-w-md mx-auto px-6">
        <div className="w-24 h-24 mx-auto bg-gradient-to-br from-accentStart to-accentEnd rounded-3xl flex items-center justify-center shadow-xl">
          <span className="text-white text-2xl font-bold">AI</span>
        </div>
        <div className="space-y-3">
          <h3 className="text-2xl font-bold text-textPrimary">Select a Platform</h3>
          <p className="text-textSecondary leading-relaxed">
            Choose a platform from the sidebar to begin. Your selected web app will load here.
          </p>
        </div>
      </div>
    </div>
  );

  if (!activePlatform) return <EmptyState />;

  if (webviewError) {
    return (
      <main className="flex-1 bg-bgPrimary relative flex flex-col items-center justify-center">
        <div className="text-center space-y-6 max-w-md mx-auto px-6">
          <div className="w-16 h-16 mx-auto bg-gradient-to-br from-error to-warning rounded-2xl flex items-center justify-center">
            <span className="text-white text-xl">⚠️</span>
          </div>
          <div className="space-y-3">
            <h3 className="text-xl font-bold text-textPrimary">Failed to Open Window</h3>
            <p className="text-textSecondary leading-relaxed">
              {webviewError}
            </p>
          </div>
          <button
            onClick={openInExternalBrowser}
            className="px-6 py-3 bg-gradient-to-r from-accentStart to-accentEnd text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-200 hover:scale-105"
          >
            Open {activePlatform.name} in Browser
          </button>
        </div>
      </main>
    );
  }

  if (isLoading) {
    return (
      <main className="flex-1 bg-bgPrimary relative">
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-bgPrimary z-10">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 border-4 border-accentStart border-t-transparent rounded-full animate-spin" />
            <p className="text-textSecondary">Opening {activePlatform.name}...</p>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="flex-1 bg-bgPrimary relative flex items-center justify-center">
      <div className="text-center space-y-4">
        <div className="w-24 h-24 mx-auto bg-gradient-to-br from-accentStart to-accentEnd rounded-3xl flex items-center justify-center shadow-xl">
          <span className="text-white text-3xl">🚀</span>
        </div>
        <h3 className="text-2xl font-bold text-textPrimary">{activePlatform.name} Opened</h3>
        <p className="text-textSecondary">The platform has been opened in a separate window.</p>
        <button
          onClick={async () => {
            if (windowRef.current) await windowRef.current.setFocus();
          }}
          className="px-4 py-2 bg-bgSecondary text-textPrimary rounded-xl hover:bg-bgTertiary transition"
        >
          Bring to Front
        </button>
      </div>
    </main>
  );
};
